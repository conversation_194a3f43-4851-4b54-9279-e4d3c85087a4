#!/usr/bin/env python3
"""
测试优惠券集成到订单创建过程的功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

from app.service.order import OrderService
from app.service.coupon_calculator import CouponCalculator
from app.schemas.order import CouponDiscountDetail, OrderItemBase

def test_coupon_calculator():
    """测试优惠券计算器的基本功能"""
    print("测试优惠券计算器...")
    
    # 创建测试订单项
    order_items = [
        OrderItemBase(
            product_id=1,
            quantity=2,
            price=10.0,
            subtotal=20.0,
            final_price=10.0,
            payable_amount=20.0,
            pricing_remark="测试商品"
        )
    ]
    
    # 测试转换方法
    products = CouponCalculator._convert_order_items_to_products(order_items)
    print(f"转换后的产品列表: {products}")
    
    # 测试优惠券详情转换
    coupon_discounts_data = [
        {
            "coupon_usage_record_id": 1,
            "coupon_id": 1,
            "coupon_name": "测试优惠券",
            "quantity": 1,
            "discount_amount": 5.0
        }
    ]
    
    coupon_discounts = CouponCalculator._convert_coupon_discounts(coupon_discounts_data)
    print(f"转换后的优惠券详情: {coupon_discounts}")
    
    print("优惠券计算器测试完成!")

def test_order_service_signature():
    """测试OrderService.create_order方法签名"""
    print("测试OrderService.create_order方法签名...")
    
    import inspect
    sig = inspect.signature(OrderService.create_order)
    print(f"方法签名: {sig}")
    
    # 检查是否有coupon_usage_record_ids参数
    params = sig.parameters
    if 'coupon_usage_record_ids' in params:
        param = params['coupon_usage_record_ids']
        print(f"coupon_usage_record_ids参数: {param}")
        print(f"默认值: {param.default}")
        print(f"类型注解: {param.annotation}")
    else:
        print("ERROR: coupon_usage_record_ids参数不存在!")
    
    print("方法签名测试完成!")

def test_schema_changes():
    """测试Schema变更"""
    print("测试Schema变更...")
    
    from app.schemas.order import OrderDetailResponse, CouponDiscountDetail
    
    # 测试CouponDiscountDetail
    discount_detail = CouponDiscountDetail(
        coupon_usage_record_id=1,
        coupon_id=1,
        coupon_name="测试优惠券",
        quantity=1,
        discount_amount=5.0
    )
    print(f"优惠券详情: {discount_detail}")
    
    # 检查OrderDetailResponse是否有新字段
    import inspect
    fields = [name for name, _ in inspect.getmembers(OrderDetailResponse)]
    
    if 'total_discount_amount' in str(OrderDetailResponse.__annotations__):
        print("✓ total_discount_amount字段已添加")
    else:
        print("✗ total_discount_amount字段未找到")
        
    if 'coupon_discounts' in str(OrderDetailResponse.__annotations__):
        print("✓ coupon_discounts字段已添加")
    else:
        print("✗ coupon_discounts字段未找到")
    
    print("Schema变更测试完成!")

if __name__ == "__main__":
    print("开始测试优惠券集成功能...")
    print("=" * 50)
    
    try:
        test_coupon_calculator()
        print()
        test_order_service_signature()
        print()
        test_schema_changes()
        print()
        print("=" * 50)
        print("所有测试完成!")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
